import service from './api'
import { getList, getDetail, create, update, remove, toggleStatus } from './baseApi'
import type { PaginationParams, PaginationResponse } from './baseApi'

// 门店接口类型
export interface Shop {
  id: number;
  shop_name: string;
  shop_logo: string;
  contact_person: string;
  contact_phone: string;
  address: string;
  business_hours: string;
  description: string;
  status: number;
  created_at: string;
  updated_at: string;
}

// 门店列表参数
export interface ShopListParams extends PaginationParams {
  shop_name?: string;
  contact_person?: string;
  contact_phone?: string;
  status?: number;
}

// 门店表单数据
export interface ShopFormData {
  id?: number;
  shop_name: string;
  shop_logo?: string;
  contact_person: string;
  contact_phone: string;
  address: string;
  business_hours: string;
  description: string;
  status: number;
}

const BASE_URL = '/company/Shop'

// 获取门店列表
export const getShopList = (params: ShopListParams = {}): Promise<PaginationResponse<Shop>> => {
  return getList<Shop>(`${BASE_URL}/index`, params)
}

// 获取门店详情
export const getShopDetail = (id: number): Promise<Shop> => {
  return getDetail<Shop>(`${BASE_URL}/edit`, id)
}

// 新增门店
export const addShop = (data: ShopFormData): Promise<any> => {
  return create(`${BASE_URL}/addPost`, data)
}

// 更新门店
export const updateShop = (data: ShopFormData): Promise<any> => {
  return update(`${BASE_URL}/editPost`, data)
}

// 删除门店
export const deleteShop = (id: number): Promise<any> => {
  return remove(`${BASE_URL}/delete`, id)
}

// 切换门店状态
export const toggleShopStatus = (id: number, status: number): Promise<any> => {
  return toggleStatus(`${BASE_URL}/status`, id, status)
}

// 获取门店选项列表（用于下拉选择）
export const getShopOptions = (): Promise<{ id: number; shop_name: string }[]> => {
  return service.get(`${BASE_URL}/options`, {
    params: {
      status: 1 // 只获取启用的门店
    }
  })
}

// 获取门店统计信息
export const getShopStats = (): Promise<{
  total_shops: number;
  active_shops: number;
  inactive_shops: number;
}> => {
  return service.get(`${BASE_URL}/stats`)
}
