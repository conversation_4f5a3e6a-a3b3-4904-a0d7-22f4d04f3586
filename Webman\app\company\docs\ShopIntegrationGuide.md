# Shop Management Integration Guide

## Overview

This guide explains how to integrate the enhanced shop management system with the existing `CsShopController.php`. The system provides both basic CRUD operations and advanced features like real-time monitoring, statistics, and admin management.

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Shop Management System                    │
├─────────────────────────────────────────────────────────────┤
│  Controllers:                                               │
│  ├── CsShopController.php (Existing - Advanced Features)    │
│  └── ShopController.php (New - Basic CRUD)                  │
├─────────────────────────────────────────────────────────────┤
│  Services:                                                  │
│  ├── ShopService.php (Basic Business Logic)                 │
│  └── EnhancedShopService.php (Advanced Features)            │
├─────────────────────────────────────────────────────────────┤
│  Middleware:                                                │
│  └── ShopAuth.php (Permission Validation)                   │
├─────────────────────────────────────────────────────────────┤
│  Models:                                                    │
│  ├── CsShop.php                                            │
│  ├── CsShopAdmin.php                                        │
│  ├── CsOrder.php                                           │
│  └── CsTable.php                                           │
└─────────────────────────────────────────────────────────────┘
```

## API Endpoints

### Basic CRUD Operations (ShopController.php)
```
GET    /company/shop/index      - Shop list with pagination
GET    /company/shop/read       - Shop details
POST   /company/shop/save       - Create new shop
POST   /company/shop/update     - Update shop
POST   /company/shop/delete     - Delete shop
POST   /company/shop/selectDel  - Batch delete shops
POST   /company/shop/state      - Toggle shop status
GET    /company/shop/options    - Shop options for dropdown
```

### Advanced Features (CsShopController.php)
```
GET    /company/shop/detail         - Detailed shop info with statistics
GET    /company/shop/status         - Real-time shop status
GET    /company/shop/statusSummary  - All shops status summary
POST   /company/shop/updateStatus   - Update shop business status
POST   /company/shop/createAdmin    - Create shop admin account
POST   /company/shop/resetPassword  - Reset admin password
GET    /company/shop/adminList      - Get shop admin list
```

## Usage Examples

### 1. Basic Shop Management

```php
// Create a new shop
$shopData = [
    'shop_name' => 'Downtown Branch',
    'nickname' => 'John Manager',
    'mobile' => '***********',
    'password' => 'secure123',
    'address' => '123 Main Street',
    'status' => 1
];

$result = ShopService::validateShopData($shopData);
$shop = CsShop::addPost($shopData);
```

### 2. Get Comprehensive Statistics

```php
// Get detailed shop statistics
$shopId = 1;
$stats = EnhancedShopService::getComprehensiveStatistics($shopId);
```

### 3. Real-time Monitoring

```php
// Get real-time shop data
$realTimeData = EnhancedShopService::getRealTimeData($shopId);
```

### 4. Admin Management

```php
// Create shop admin
$adminData = [
    'username' => 'shop_admin',
    'password' => 'admin123',
    'nickname' => 'Shop Administrator',
    'mobile' => '***********'
];

$result = EnhancedShopService::createShopAdmin($shopId, $adminData);
```

## Integration Steps

1. **Use existing CsShopController for advanced features**
2. **Add new ShopController for basic CRUD if needed**
3. **Implement ShopService for data validation**
4. **Use EnhancedShopService for advanced statistics**
5. **Add ShopAuth middleware for security**

## Best Practices

- Always validate data before processing
- Use transactions for critical operations
- Log important operations
- Handle exceptions gracefully
- Implement proper permission checks
- Cache frequently accessed data
- Use real-time updates for monitoring