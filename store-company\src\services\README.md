# Store-Company 前端请求工具说明

## 概述

本项目的前端请求工具参考了 store-shop 项目的实现，提供了完整的请求拦截和响应拦截功能，支持自动处理认证、错误处理等常见场景。

## 文件结构

```
src/services/
├── api.ts              # 基础 axios 配置和拦截器
├── authApi.ts          # 认证相关 API
├── baseApi.ts          # 通用 API 工具方法
├── adminApi.ts         # 管理员相关 API
├── roleApi.ts          # 角色相关 API
├── shopApi.ts          # 门店相关 API
├── index.ts            # 统一导出文件
├── examples.ts         # 使用示例
└── README.md           # 说明文档
```

## 核心特性

### 1. 请求拦截器

- **自动添加公共 headers**：
  - `Accept: application/json`
  - `timestamp`: 当前时间戳
  - `apikey`: 根据时间戳生成的加密key
  - `token`: 用户登录token（登录和注册接口除外）

- **Token 管理**：
  - 自动从 `localStorage` 获取 `company_token`
  - 登录和注册接口自动跳过token验证

### 2. 响应拦截器

- **统一错误处理**：
  - `code === 1`: 成功，直接返回 `data` 字段
  - `code === 2`: 业务错误，显示错误消息并抛出异常
  - `code === 1003`: 登录过期，清除认证数据并跳转登录页
  - 其他错误: 显示通用错误消息

- **文件下载支持**：
  - 自动识别 blob 响应类型
  - 直接返回文件数据

### 3. API Key 生成

按照项目API规则生成apikey：
1. 时间戳 + 'ca89ed' 进行 MD5 加密
2. 第一步结果 + 'a77f0a3eb0955b2147dabaae883848af' 再次 MD5 加密

## 使用方法

### 1. 基础使用

```typescript
import { service } from '@/services'

// 直接使用 axios 实例
const response = await service.get('/company/Admin/index')
const result = await service.post('/company/Admin/addPost', data)
```

### 2. 使用封装的 API 方法

```typescript
import { getAdminList, addAdmin } from '@/services'

// 获取管理员列表
const adminList = await getAdminList({
  page: 1,
  page_size: 10,
  username: 'admin'
})

// 新增管理员
const result = await addAdmin({
  username: 'newadmin',
  nickname: '新管理员',
  password: '123456',
  mobile: '13800138000',
  role_id: 2,
  status: 1
})
```

### 3. 使用通用 API 工具

```typescript
import { getList, getDetail, create, update, remove } from '@/services'

// 通用列表查询
const list = await getList('/company/CustomModule/index', {
  page: 1,
  page_size: 10
})

// 通用详情查询
const detail = await getDetail('/company/CustomModule/edit', 1)

// 通用新增
const result = await create('/company/CustomModule/addPost', data)
```

## API 接口规范

### 请求格式

- **基础URL**: `http://127.0.0.1:8787`
- **接口路径**: `/company/模块名/方法名`
- **请求方式**: GET（列表和查看接口）、POST（其他接口）

### 响应格式

```json
{
  "code": 1,
  "msg": "ok",
  "data": {
    "itemList": [...],
    "total": 100,
    "pageSize": 10
  }
}
```

### 分页参数

所有列表接口支持以下分页参数：
- `page`: 当前页码，默认为1
- `page_size`: 每页显示数量，默认为10
- `sort_field`: 排序字段，默认为id
- `sort`: 排序规则，默认为desc

## 错误处理

### 1. 网络错误

网络连接失败时会显示"服务器连接失败"消息。

### 2. 业务错误

API返回 `code !== 1` 时会自动显示错误消息，无需在组件中重复处理。

### 3. 登录过期

API返回 `code === 1003` 时会：
1. 显示"登录过期"消息
2. 清除本地认证数据
3. 自动跳转到登录页

## 在 Vue 组件中使用

```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getAdminList, type Admin } from '@/services'

const adminList = ref<Admin[]>([])
const loading = ref(false)

const fetchData = async () => {
  try {
    loading.value = true
    const result = await getAdminList({ page: 1, page_size: 10 })
    adminList.value = result.itemList
  } catch (error) {
    // 错误已在拦截器中处理，这里可以做额外处理
    console.error('获取数据失败:', error)
  } finally {
    loading.value = false
  }
}

onMounted(fetchData)
</script>
```

## 扩展新的 API

### 1. 创建新的 API 文件

```typescript
// src/services/newModuleApi.ts
import service from './api'
import { getList, getDetail, create, update, remove } from './baseApi'

export interface NewModule {
  id: number
  name: string
  status: number
}

const BASE_URL = '/company/NewModule'

export const getNewModuleList = (params = {}) => {
  return getList<NewModule>(`${BASE_URL}/index`, params)
}

export const addNewModule = (data: Partial<NewModule>) => {
  return create(`${BASE_URL}/addPost`, data)
}
```

### 2. 在 index.ts 中导出

```typescript
// src/services/index.ts
export * from './newModuleApi'
```

## 注意事项

1. **Token 存储**: 使用 `company_token` 作为 localStorage key，区别于 store-shop 项目的 `token`
2. **错误处理**: 响应拦截器已处理大部分错误场景，组件中通常只需处理业务逻辑
3. **类型安全**: 所有 API 方法都提供了 TypeScript 类型定义
4. **统一规范**: 遵循项目 API 规则文档中定义的接口格式
