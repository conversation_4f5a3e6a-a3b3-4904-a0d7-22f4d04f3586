import service from './api'

// 通用分页参数接口
export interface PaginationParams {
  page?: number;
  page_size?: number;
  sort_field?: string;
  sort?: string;
}

// 通用分页响应接口
export interface PaginationResponse<T> {
  itemList: T[];
  total: number;
  pageSize: number;
}

// 通用列表查询方法
export const getList = <T>(
  url: string, 
  params: PaginationParams & Record<string, any> = {}
): Promise<PaginationResponse<T>> => {
  // 设置默认分页参数
  const defaultParams = {
    page: 1,
    page_size: 10,
    sort_field: 'id',
    sort: 'desc',
    ...params
  }
  
  return service.get(url, { params: defaultParams })
}

// 通用详情查询方法
export const getDetail = <T>(url: string, id: number): Promise<T> => {
  return service.get(url, { params: { id } })
}

// 通用新增方法
export const create = <T>(url: string, data: T): Promise<any> => {
  return service.post(url, data)
}

// 通用更新方法
export const update = <T>(url: string, data: T & { id: number }): Promise<any> => {
  return service.post(url, data)
}

// 通用删除方法
export const remove = (url: string, id: number): Promise<any> => {
  return service.post(url, { id })
}

// 通用批量删除方法
export const batchRemove = (url: string, ids: number[]): Promise<any> => {
  return service.post(url, { ids })
}

// 通用状态切换方法
export const toggleStatus = (url: string, id: number, status: number): Promise<any> => {
  return service.post(url, { id, status })
}
