import axios from 'axios'
import md5 from 'crypto-js/md5'
import type { InternalAxiosRequestConfig, AxiosResponse } from 'axios'
import { ElMessage } from 'element-plus'

// 定义接口返回类型
export interface LoginResponse {
  token: string;
  id: number;
  username: string;
  nickname: string;
  avatar: string;
  mobile: string;
  company_id: number;
  unread_message_count: number;
  [key: string]: any;
}

// 基础URL
export const BASE_URL = 'http://127.0.0.1:8787'
// 上传接口URL
export const UPLOAD_URL = `${BASE_URL}/upload/upload`

// 创建 axios 实例
const service = axios.create({
  baseURL: BASE_URL, // 统一基础地址
  timeout: 10000
})

// 生成 apikey
function generateApiKey(timestamp: number): string {
  const step1 = md5(timestamp + 'ca89ed').toString()
  const step2 = md5(step1 + 'a77f0a3eb0955b2147dabaae883848af').toString()
  return step2
}

// 请求拦截器
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 获取时间戳
    const timestamp = Math.floor(Date.now() / 1000)
    // 生成 apikey
    const apikey = generateApiKey(timestamp)

    // 设置公共 headers
    config.headers.Accept = 'application/json'
    config.headers.timestamp = timestamp
    config.headers.apikey = apikey

    // 如果有 token，添加到 headers（登录和注册接口除外）
    const token = localStorage.getItem('company_token')
    const isLoginOrRegister = config.url?.includes('login') || config.url?.includes('register')

    if (token && !isLoginOrRegister) {
      config.headers.token = token
    }

    return config
  },
  (error: unknown) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data

    // 如果是blob响应（文件下载），直接返回
    if (response.config.responseType === 'blob') {
      return res
    }

    // 判断返回状态码
    if (res.code === 1) {
      // 成功，返回 data 数据
      return res.data
    } else if (res.code === 2) {
      // 显示错误消息并添加标记
      ElMessage.error(res.msg || '操作失败')
      const error = new Error(res.msg || '操作失败')
      // 添加标记，表示已经显示过错误消息
      Object.defineProperty(error, 'messageShown', { value: true })
      return Promise.reject(error)
    } else if (res.code === 1003) {
      // 登录过期
      ElMessage.error(res.msg || '登录过期，请重新登录')
      
      // 清除认证数据
      localStorage.removeItem('company_token')
      localStorage.removeItem('company_user_info')
      
      // 跳转到登录页
      window.location.href = '/login'
      const error = new Error(res.msg || '登录过期')
      Object.defineProperty(error, 'messageShown', { value: true })
      return Promise.reject(error)
    } else {
      // 其他错误情况
      ElMessage.error(res.msg || '未知错误')
      const error = new Error(res.msg || '未知错误')
      Object.defineProperty(error, 'messageShown', { value: true })
      return Promise.reject(error)
    }
  },
  (error: unknown) => {
    // 处理网络错误等
    let message = '服务器连接失败'

    if (error instanceof Error) {
      message = error.message
    }

    // 只有在未显示过错误消息时才显示
    if (!Object.prototype.hasOwnProperty.call(error, 'messageShown')) {
      ElMessage.error(message)
    }

    return Promise.reject(error)
  }
)

// 登录方法
export async function login(data: { username: string; password: string }): Promise<LoginResponse> {
  return service.post<any, LoginResponse>('/company/Login/login', data)
}

// 上传方法
export async function upload(data: { file: any; width: number; height: number }) {
  return service.post('/upload/upload', data)
}

export default service
