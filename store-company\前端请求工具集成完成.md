# Store-Company 前端请求工具集成完成

## 完成概述

已成功为 store-company 项目创建了完整的前端请求工具，参考了 store-shop 项目的实现，提供了请求拦截、响应拦截以及完整的API封装。

## 已完成的工作

### 1. 依赖安装
- ✅ 安装 `axios` - HTTP请求库
- ✅ 安装 `crypto-js` - 用于生成API密钥
- ✅ 安装 `@types/crypto-js` - TypeScript类型定义

### 2. 核心API工具 (`src/services/`)

#### 2.1 基础配置 (`api.ts`)
- ✅ 创建axios实例，配置基础URL和超时时间
- ✅ 实现请求拦截器：
  - 自动添加公共headers (Accept, timestamp, apikey)
  - 自动添加token (除登录注册接口外)
  - 按照API规则生成apikey加密
- ✅ 实现响应拦截器：
  - 统一处理成功响应 (code === 1)
  - 统一处理业务错误 (code === 2)
  - 统一处理登录过期 (code === 1003)
  - 支持文件下载 (blob响应)
- ✅ 提供登录和上传方法

#### 2.2 认证API (`authApi.ts`)
- ✅ 登录接口
- ✅ 获取用户信息接口
- ✅ 退出登录接口
- ✅ 修改密码接口
- ✅ 修改个人信息接口

#### 2.3 通用API工具 (`baseApi.ts`)
- ✅ 通用分页查询方法
- ✅ 通用详情查询方法
- ✅ 通用新增方法
- ✅ 通用更新方法
- ✅ 通用删除方法
- ✅ 通用批量删除方法
- ✅ 通用状态切换方法

#### 2.4 业务API封装
- ✅ **管理员API** (`adminApi.ts`): 列表、详情、新增、更新、删除、状态切换、重置密码
- ✅ **角色API** (`roleApi.ts`): 列表、详情、新增、更新、删除、权限树、角色选项
- ✅ **门店API** (`shopApi.ts`): 列表、详情、新增、更新、删除、门店选项、统计信息

#### 2.5 统一导出 (`index.ts`)
- ✅ 统一导出所有API方法和类型定义

### 3. 认证状态管理更新
- ✅ 更新 `stores/auth.ts` 使用真实API
- ✅ 修改token存储key为 `company_token`
- ✅ 修改用户信息存储key为 `company_user_info`
- ✅ 实现真实的登录、退出、用户信息获取逻辑

### 4. 开发工具和文档

#### 4.1 使用示例 (`examples.ts`)
- ✅ 提供各种API使用示例
- ✅ 展示在Vue组件中的完整使用方法

#### 4.2 测试工具 (`test.ts`)
- ✅ 创建API测试方法
- ✅ 在开发环境自动加载到 `window.testApi`
- ✅ 提供便捷的测试接口

#### 4.3 示例组件 (`ApiExample.vue`)
- ✅ 创建完整的Vue组件示例
- ✅ 展示各种API的实际使用方法
- ✅ 包含错误处理演示

#### 4.4 完整文档 (`README.md`)
- ✅ 详细的使用说明
- ✅ API规范说明
- ✅ 错误处理说明
- ✅ 扩展指南

### 5. 开发环境集成
- ✅ 在 `main.ts` 中集成测试工具
- ✅ 开发环境下自动提示使用方法

## 核心特性

### 1. 完全兼容API规则
- ✅ 基础URL: `http://127.0.0.1:8787`
- ✅ 接口路径: `/company/模块名/方法名`
- ✅ 自动生成apikey (timestamp + 'ca89ed' -> MD5 -> + 'a77f0a3eb0955b2147dabaae883848af' -> MD5)
- ✅ 自动添加必需的headers (Accept, timestamp, apikey, token)

### 2. 智能错误处理
- ✅ 自动显示错误消息
- ✅ 登录过期自动跳转
- ✅ 防止重复显示错误消息
- ✅ 网络错误统一处理

### 3. TypeScript支持
- ✅ 完整的类型定义
- ✅ 接口参数类型检查
- ✅ 响应数据类型推断

### 4. 开发友好
- ✅ 详细的使用文档
- ✅ 丰富的代码示例
- ✅ 便捷的测试工具
- ✅ 清晰的文件结构

## 使用方法

### 1. 基础使用
```typescript
import { getAdminList, addAdmin } from '@/services'

// 获取列表
const list = await getAdminList({ page: 1, page_size: 10 })

// 新增数据
const result = await addAdmin(formData)
```

### 2. 在Vue组件中使用
```vue
<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getAdminList, type Admin } from '@/services'

const adminList = ref<Admin[]>([])

const fetchData = async () => {
  try {
    const result = await getAdminList()
    adminList.value = result.itemList
  } catch (error) {
    // 错误已自动处理
  }
}

onMounted(fetchData)
</script>
```

### 3. 开发测试
在浏览器控制台中运行：
```javascript
// 运行所有测试
window.testApi.runAllTests()

// 测试特定功能
window.testApi.testLoginApi()
```

## 下一步工作

1. **后端API开发**: 根据前端封装的接口创建对应的后端API
2. **接口联调**: 启动后端服务后进行接口测试和调试
3. **业务集成**: 在实际的业务页面中集成这些API
4. **错误处理优化**: 根据实际使用情况优化错误处理逻辑

## 文件清单

```
store-company/src/services/
├── api.ts                  # 基础axios配置和拦截器
├── authApi.ts             # 认证相关API
├── baseApi.ts             # 通用API工具
├── adminApi.ts            # 管理员API
├── roleApi.ts             # 角色API
├── shopApi.ts             # 门店API
├── index.ts               # 统一导出
├── examples.ts            # 使用示例
├── test.ts                # 测试工具
└── README.md              # 详细文档

store-company/src/components/
└── ApiExample.vue         # Vue组件使用示例

store-company/src/stores/
└── auth.ts                # 更新后的认证状态管理

store-company/
└── 前端请求工具集成完成.md  # 本文档
```

## 总结

前端请求工具已完全集成完成，提供了：
- 🔧 完整的API封装
- 🛡️ 智能的错误处理
- 📝 详细的文档说明
- 🧪 便捷的测试工具
- 💡 丰富的使用示例

现在可以开始后端API的开发工作，并使用这套前端工具进行接口对接。
