import service from './api'
import { getList, getDetail, create, update, remove, toggleStatus } from './baseApi'
import type { PaginationParams, PaginationResponse } from './baseApi'

// 管理员接口类型
export interface Admin {
  id: number;
  username: string;
  nickname: string;
  avatar: string;
  mobile: string;
  role_id: number;
  role_name?: string;
  status: number;
  created_at: string;
  updated_at: string;
}

// 管理员列表参数
export interface AdminListParams extends PaginationParams {
  username?: string;
  nickname?: string;
  mobile?: string;
  role_id?: number;
  status?: number;
}

// 管理员表单数据
export interface AdminFormData {
  id?: number;
  username: string;
  nickname: string;
  password?: string;
  avatar?: string;
  mobile: string;
  role_id: number;
  status: number;
}

const BASE_URL = '/company/Admin'

// 获取管理员列表
export const getAdminList = (params: AdminListParams = {}): Promise<PaginationResponse<Admin>> => {
  return getList<Admin>(`${BASE_URL}/index`, params)
}

// 获取管理员详情
export const getAdminDetail = (id: number): Promise<Admin> => {
  return getDetail<Admin>(`${BASE_URL}/edit`, id)
}

// 新增管理员
export const addAdmin = (data: AdminFormData): Promise<any> => {
  return create(`${BASE_URL}/addPost`, data)
}

// 更新管理员
export const updateAdmin = (data: AdminFormData): Promise<any> => {
  return update(`${BASE_URL}/editPost`, data)
}

// 删除管理员
export const deleteAdmin = (id: number): Promise<any> => {
  return remove(`${BASE_URL}/delete`, id)
}

// 切换管理员状态
export const toggleAdminStatus = (id: number, status: number): Promise<any> => {
  return toggleStatus(`${BASE_URL}/status`, id, status)
}

// 重置管理员密码
export const resetAdminPassword = (id: number, password: string): Promise<any> => {
  return service.post(`${BASE_URL}/resetPassword`, { id, password })
}
